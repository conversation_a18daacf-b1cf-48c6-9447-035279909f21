<g:render template="/wonderpublish/commonfooter_new"></g:render>
<% if("55".equals(""+session['siteId'])){ %>
<asset:stylesheet href="whitelabel/additionalStyles.css"/>
<%}%>
<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        userLoggedIn=true;
    </script>
</sec:ifLoggedIn>
<style>
<%if(session['wileySite'] != true){%>
.copy-right-text-footer span{
    color: grey !important;
}
<%}%>
</style>

<%if(session['wileySite'] == true){%>
<style>

@media (min-width: 768px) {
    .page-main-wrapper.ebooks{
        min-height: 75vh;
    }
}
.whitelabel-site .connect-section{
    background: #fff !important;
}
ul.link-ul-footer li a{
    font-weight: 500;
}
</style>
<%}%>
<section class="showrank-modal">
    <div class="modal" id="rank-dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h4 class="modal-title">Rank</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="content-rankWrapper">
                        <div class="container">
                            <div class="d-flex justify-content-around align-items-center">
                                <div>
                                    <div class="profile">  <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                        <%}%></div>
                                    <p class="yr-head">Your Score</p>
                                    <p class="no-ques" id="userScore"></p>
                                </div>
                                <div>
                                    <div>
                                        <p class="rank-head">Your Rank</p>
                                        <p class="user-rank" id="userRank"></p>
                                        <p class="yr-head" id="totalParticipants"></p>
                                        <p id="detailedResults"></p>
                                        <!--   <p class="total-students" id="attemptedBy">Attempted by 2024 students</p>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container mt-4">
                        <table class="table text-center table-hover">
                            <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Name</th>
                                <th>Score</th>
                            </tr>
                            </thead>
                            <tbody id="rankDetails">


                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="modal-footer">

                </div>

            </div>
        </div>
    </div>
</section>
<% if(params.tokenId==null && !isBookPage){%>
<% if("55".equals(""+session['siteId'])){ %>
<footer class="educart__footer">
    <div class="educart__footer-info">
        <div class="educart__footer-info__content">
            <img src="${assetPath(src: 'privatelabels/educart_white.svg')}" alt="Educart Logo" class="footerLogo"/>
            <div class="educart__footer-info__content top">
                <ul>
                    <li>
                        <a href="https://amzn.to/3RijOjr" title="Amazon link">
                            <img src="${assetPath(src: 'privatelabels/educart_amazon.svg')}" alt="Amazon link" />
                        </a>
                    </li>
                    <li>
                        <a href="https://bit.ly/3LjItAd" title="Flipkart link">
                            <img src="${assetPath(src: 'privatelabels/educart_flipkart.svg')}" alt="Flipkart link" />
                        </a>
                    </li>
                    <li>
                        <a href="https://www.facebook.com/educartbooks/" title="FB link">
                            <img src="${assetPath(src: 'privatelabels/educart_fb.svg')}" alt="FB link" />
                        </a>
                    </li>
                    <li>
                        <a href="https://www.instagram.com/educartbooks/" title="Insta link">
                            <img src="${assetPath(src: 'privatelabels/educart_insta.svg')}" alt="Insta link" />
                        </a>
                    </li>
                    <li>
                        <a href="https://bit.ly/3tmWWW7" title="Telegram link">
                            <img src="${assetPath(src: 'privatelabels/educart_tel.svg')}" alt="Telegram link" />
                        </a>
                    </li>
                </ul>
            </div>
            <p class="footer_connect">Connect with us</p>
            <div class="footer_contactInfo">
                <a href="tel:+918088443860"><i class="fa-solid fa-phone"></i> +91 8088443860</a>
                <a href="mailto:<EMAIL>"><i class="fa-solid fa-envelope"></i> <EMAIL></a>
            </div>
        </div>
        <hr class="horLineOne">
        <div class="educart__footer-info__content">
            <p class="educart__footer-about__title">Categories</p>
            <div id="footer__categories" class="d-flex" style="gap: 2rem">

            </div>

        </div>
        <div class="educart__footer-info__content bottom">
            <p class="educart__footer-about__title">Follow Us</p>
            <ul>
                <li>
                    <a href="https://amzn.to/3RijOjr" title="Amazon link">
                        <img src="${assetPath(src: 'privatelabels/educart_amazon.svg')}" alt="Amazon link" />
                    </a>
                </li>
                <li>
                    <a href="https://bit.ly/3LjItAd" title="Flipkart link">
                        <img src="${assetPath(src: 'privatelabels/educart_flipkart.svg')}" alt="Flipkart link" />
                    </a>
                </li>
                <li>
                    <a href="https://www.facebook.com/educartbooks/" title="FB link">
                        <img src="${assetPath(src: 'privatelabels/educart_fb.svg')}" alt="FB link" />
                    </a>
                </li>
                <li>
                    <a href="https://www.instagram.com/educartbooks/" title="Insta link">
                        <img src="${assetPath(src: 'privatelabels/educart_insta.svg')}" alt="Insta link" />
                    </a>
                </li>
                <li>
                    <a href="https://bit.ly/3tmWWW7" title="Telegram link">
                        <img src="${assetPath(src: 'privatelabels/educart_tel.svg')}" alt="Telegram link" />
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <hr class="horLine">
    <div class="educart__footer-about">
        <p class="educart__footer-about__title">About Educart</p>
        <p class="educart__footer-about__content">
            At Educart, we aim to provide children with the best resources for K12 and competitive exams like CUET and NEET. The dedicated team of
            authors, editors, and project managers is responsible for providing
            high-quality content as per the latest educational trends.
        </p>
        <p class="educart__footer-about__content">
            Emerging as the foremost choice for teachers as well as students, we
            strive to improve and do better than before. Learners can find
            themselves staying updated with the changes occurring in
            educational trends, from examination timetables to the results,
            everything is here.
        </p>
    </div>

    <div class="educart__footer-legal">
        <p class="copy-right-text-footer"></p>
    </div>
</footer>
<%}else {%>
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4 responsive-none-view">
                <%if(session['wileySite'] != true){%>
                <div class="image-wrapper-footer-logo">
                    <img src="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["logo"]}" alt="MTG Logo" />
                </div>
                <div class="text-center-align-here">
                    <ul class="there-social-footer-link-wrp">
                        <% if(session["facebookLink"]) { %>
                        <li><a href="${session["facebookLink"]}" target="_blank"><i class="fa fa-facebook"></i></a></li>
                        <% } %>
                        <% if(session["twitterLink"]) { %>
                        <li><a href="${session["twitterLink"]}" target="_blank"><i class="fa fa-twitter"></i></a></li>
                        <% } %>
                        <% if(session["instagramLink"]) { %>
                        <li><a href="${session["instagramLink"]}" target="_blank"><i class="fa fa-instagram"></i></a></li>
                        <% } %>
                        <% if(session["linkedinLink"]) { %>
                        <li><a href="${session["linkedinLink"]}" target="_blank"><i class="fa fa-linkedin"></i></a></li>
                        <% } %>
                        <% if(session["youtubeLink"]) { %>
                        <li><a href="${session["youtubeLink"]}" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
                        <% } %>
                        <% if(session["whatsappLink"]) { %>
                        <li><a href="${session["whatsappLink"]}" target="_blank"><i class="fa fa-whatsapp"></i></a></li>
                        <% } %>
                        <% if(session["telegramLink"]) { %>
                        <li><a href="${session["telegramLink"]}" target="_blank"><i class="fa fa-telegram"></i></a></li>
                        <% } %>
                    </ul>
                </div>
                <%}%>
            </div>

            <%if(session['wileySite'] != true){%>
            <div class="col-md-8 responsive-view-none">
                <div class="row">
            <%}else{%>
            <div class="col-md-12 responsive-view-none">
                <div class="row justify-content-center">
            <%}%>
                    <%if(session['wileySite'] != true&&!"ibookso".equals(session['siteName'])&&!"Yes".equals(""+session["disableStore"])){%>
                    <div class="col-lg-3 col-md-6 main-div-box-link-footer">
                    <%if("82".equals(""+session["siteId"])){%>
                    <h3 class="footer-link-title">Universities</h3>
                    <%}else{%>
                    <h3 class="footer-link-title">Categories</h3>
                    <%}%>
                        <ul class="link-ul-footer" id="footerCategoryLinks"></ul>
                    </div>
                    <%}%>
                    <%if("ibookso".equals(session['siteName'])){%>
                    <div class="col-lg-3 col-md-6 main-div-box-link-footer">
                        <h3 class="footer-link-title">Products</h3>
                        <ul class="link-ul-footer" id="products">
                            <li><a href="/printbooks?siteName=ibookso" target="_blank">Print Books</a> </li>
                            <li><a href="/electronics?siteName=ibookso" target="_blank">Laptops and Mobiles</a> </li>
                            <li><a href="/video-games?siteName=ibookso" target="_blank">Video Games</a> </li>
                            <li><a href="/office-supplies?siteName=ibookso" target="_blank">Office Supplies</a></li>
                            <li><a href="/sports-and-fitness?siteName=ibookso" target="_blank">Sports & Fitness</a> </li>
                            <li><a href="/bags-wallets-luggage?siteName=ibookso" target="_blank">Bags</a> </li>
                            <li><a href="/toys-and-games?siteName=ibookso" target="_blank">Toys & Games</a> </li>
                            <li><a href="/shoes?siteName=ibookso" target="_blank">Shoes</a> </li>
                        </ul>
                    </div>
                    <%}%>


                    <%if(session['wileySite'] == true){%>
                        <div class="col-lg-3 col-md-6 main-div-box-link-footer">
                            <ul class="link-ul-footer d-flex" id="footerPolicyLinks" style="justify-content: space-evenly">
                                <li><a href="${session["privacyPolicy"] ? session["privacyPolicy"] : "https://www.wonderslate.com/funlearn/privacy"}" target="_blank">Privacy Policy</a></li>
                                <li><a href="${session["termsCondition"] ? session["termsCondition"] : "https://www.wonderslate.com/funlearn/termsandconditions"}" target="_blank">Terms of use</a></li>
                            </ul>
                        </div>
                    <%}else{%>
                        <div class="col-lg-3 col-md-6 main-div-box-link-footer">
                            <h3 class="footer-link-title">Our Policy</h3>
                            <ul class="link-ul-footer" id="footerPolicyLinks">
                                <li><a href="${session["privacyPolicy"] ? session["privacyPolicy"] : "https://www.wonderslate.com/funlearn/privacy"}" target="_blank">Privacy Policy</a></li>
                                <li><a href="${session["termsCondition"] ? session["termsCondition"] : "https://www.wonderslate.com/funlearn/termsandconditions"}" target="_blank">Terms & Conditions</a></li>
                            </ul>
                        </div>
                    <%}%>
                    <div class="col-lg-3 col-md-6 main-div-box-link-footer d-none" id="customMenuWrap">
                        <h3 class="footer-link-title">Quick Links</h3>
                        <ul class="link-ul-footer" id="customPageFooterLink">
                        </ul>
                    </div>

                    <% if(session["playStore"] || session["appStore"]) { %>
                    <div class="col-lg-3 col-md-6 main-div-box-link-footer download-app-links text-center text-md-left">
                        <h3 class="footer-link-title">Download App From</h3>
                        <ul class="link-ul-footer mt-3">
                            <% if(session["playStore"]) { %>
                            <li>
                                <a href="${session["playStore"]}" target="_blank" class="d-inline-flex align-items-center justify-content-center android-app-link text-left">
                                    <img src="${assetPath(src: 'playstore.png')}" alt="">
                                    <span>
                                        <small>Google</small><br>Play Store
                                    </span>
                                </a>
                            </li>
                            <% } %>
                            <% if(session["appStore"]) { %>
                            <li class="mt-2">
                                <a href="${session["appStore"]}" target="_blank" class="d-inline-flex align-items-center justify-content-center android-app-link text-left">
                                    <img src="${assetPath(src: 'appstore.png')}" alt="">
                                    <span>
                                        <small>iOS</small><br>App Store
                                    </span>
                                </a>
                            </li>
                            <% } %>
                        </ul>
                    </div>
                    <% } %>

                    <% if(session['enableContactus'] && "true".equals(session['enableContactus']) && !"".equals(session["mobileNumber"])){%>
                        <div class="col-lg-3 col-md-6 main-div-box-link-footer" id="customContact">
                            <h3 class="footer-link-title">Contact us</h3>
                            <%
                                    String number = session["mobileNumber"]
                                    def numbers = number.split(",\\s*")
                            %>

                            <div class="d-flex flex-column mt-2 cus-mob">
                                <% numbers.eachWithIndex { mobileNumber, index -> %>
                                <a href="tel:${mobileNumber.trim()}" id="mobile_${index}">${mobileNumber.trim()}</a>
                                <% } %>
                            </div>

                            <h3 class="footer-link-title mt-4 cus-wp">Whatsapp</h3>
                            <div class="d-flex flex-column mt-2">
                                <% numbers.eachWithIndex { mobileNumber, index -> %>
                                <a href="https://wa.me/${mobileNumber.trim()}" id="wp_mobile_${index}">${mobileNumber.trim()}</a>
                                <% } %>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <div class="row footer-copyright mt-2 mt-md-4 mx-0">
        <div class="container">
            <div class="row">
                <div class="col-12 d-flex justify-content-between align-items-center footer_wrap-div">
                    <p class="copy-right-text-footer"></p>
                    <%if(visitorCounter!=null&&!"0".equals(visitorCounter)){%>
                        <p class="text-dark mb-0 visitorsCount"><i class="fa-regular fa-eye"></i> Total visits: <span class="usageCount">${visitorCounter}</span></p>
                    <%}%>
                </div>
            </div>
        </div>
    </div>
</footer>
<%}%>
<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>

<script>
    //Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    var nextYear = (new Date().getFullYear()+1);
    var twoDigitYear = nextYear.toString().substr(-2);
    <%if("89".equals(""+session["siteId"])||"90".equals(""+session["siteId"])){%>
    $('.copy-right-text-footer').html('Copyright &copy; ' + shortYear +'-'+twoDigitYear+". <span> Powered by Noetic Technologies</span> ");
    <%}else{%>
    $('.copy-right-text-footer').html('Copyright &copy; ' + shortYear +'-'+twoDigitYear+". <span> Powered by Wonderslate</span> ");
    <%}%>

    var footerCategoryLinks="";
    for (var i = 0; i < activeCategories.length; i++) {
        footerCategoryLinks +="<li><a href='/sp/${session["siteName"]}/store?level="+replaceAll(activeCategories[i].level.replace('&', '~'),' ','-')+"'>"+activeCategories[i].level+"</a></li>";
    }
    <% if(params.tokenId==null && !isBookPage){%>
    if (document.getElementById("footerCategoryLinks")!=null){
        document.getElementById("footerCategoryLinks").innerHTML=footerCategoryLinks;
    }
    <%}%>

    var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

    var displayLevel = "";
    var categoryChanged = true;
    var categorySyllabusHtml="";
    for (var i = 0; i < activeCategoriesSyllabus.length; i++) {
        if(activeCategoriesSyllabus[i].level!=displayLevel){
            categoryChanged = true;
            displayLevel = activeCategoriesSyllabus[i].level;
            //closing tag logic
            if(displayLevel!=""){
                categorySyllabusHtml +=" </ul>\n" +
                    "  </div>\n" +
                    "  </div>";
            }
            categorySyllabusHtml +="<div class=\"col-sm-4\">\n" +
                "          <div class=\"manage-row-fluides-menu-big\">\n" +
                "          <ul class=\"manage-with-all-links-big-menus\">\n" +
                "          <h4>"+activeCategoriesSyllabus[i].level+"</h4>";
        }
        if("Medical Entrances"==activeCategoriesSyllabus[i].level&&"NEET"==activeCategoriesSyllabus[i].syllabus) continue;
        categorySyllabusHtml +="<li><a href='/sp/${session["siteName"]}/store?level="+ replaceAll(activeCategoriesSyllabus[i].level.replace('&', '~'),' ','-')+"&syllabus="+replaceAll(activeCategoriesSyllabus[i].syllabus.replace('&', '~'),' ','-')+"&grade=null'>"+activeCategoriesSyllabus[i].syllabus+"</a></li>";
    }
    //last tag close
    categorySyllabusHtml +=" </ul>\n" +
        "  </div>\n" +
        "  </div>";
    const groupedDataFooter = activeCategoriesSyllabus.reduce((acc, obj) => {
        const key = obj.level;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    let footerHtml = '';
    for (const key in groupedDataFooter) {
        const group = groupedDataFooter[key];
        footerHtml += "<div>"+
            "<p><a href='/sp/${session["siteName"]}/store?level="+encodeURIComponent(key)+"' style='font-size: 14px;font-weight: bold;color:#fff'>"+
            key+"</a></p>";
        footerHtml += "<ul class='catList'>";
        for (const obj of group) {
            footerHtml +="<li>" +
                "<a href='/sp/${session["siteName"]}/store?level="+encodeURIComponent(key)+"&syllabus="+encodeURIComponent(obj.syllabus)+"' style='font-size: 13px;'>"+ obj.syllabus+"</a></li>";
        }
        footerHtml += '</ul>'+
            "</div>";
    }
    if(document.getElementById('footer__categories')){
        document.getElementById('footer__categories').innerHTML = footerHtml;
    }
</script>
<script>
    <%String footerStoreText = session['wileySite'] ? 'Catalog' : 'Store' %>
    var mobileFooterNav =
        '        <div class="d-flex row justify-content-around w-100">\n' +
        '            <a href="/sp/${session['siteName']}/store" class="ebooks-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
        '                <p>${footerStoreText}</p>\n' +
        '            </a>\n' +
        '<sec:ifNotLoggedIn>\n' +
        '            <a href="javascript:loginOpen()" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'whitelabel/icon-whitelabel-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'whitelabel/icon-whitelabel-library-filled.svg')}">\n' +
        <% if("71".equals(""+session['siteId'])){ %>
        '                <p>My Tests</p>\n' +
        <%}else{%>
        '                <p>My Books</p>\n' +
        <%}%>
        '            </a>\n' +
        '</sec:ifNotLoggedIn>'+
        '<sec:ifLoggedIn>'+
        '            <a href="/wsLibrary/myLibrary" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'whitelabel/icon-whitelabel-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'whitelabel/icon-whitelabel-library-filled.svg')}">\n' +
        <% if("71".equals(""+session['siteId'])){ %>
        '                <p>My Tests</p>\n' +
        <%}else{%>
        '                <p>My Books</p>\n' +
        <%}%>
        '            </a>\n' +
        '</sec:ifLoggedIn>'+
        '            <a href="/liveMockTests" class="ebooks-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
        '                <p>Live Mock Tests</p>\n' +
        '            </a>\n' +
        '        </div>';

    $(document).ready(function(){
        document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

        var url = window.location.href;
        if(url.indexOf("/funlearn/quiz") != -1){
            //$('.mobile-footer-nav').addClass('hide-menus');
        } else if(url.indexOf("/wsLibrary/myLibrary") != -1){
            $('.mobile-footer-nav .home-menu').addClass('active-menu');
            $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
        } else if(url.indexOf("/sp/${session['siteName']}/store") != -1){
            $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
            $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu').addClass('common-footer-nav');
        } else {
            $('.mobile-footer-nav a').addClass('common-footer-nav');
        }

    });

</script>
<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="whitelabel/popper.min.js"/>
<asset:javascript src="whitelabel/bootstrap.min.js"/>
<asset:javascript src="whitelabel/jquery-ui.min.js"/>
<asset:javascript src="landingpage/slick.js"/>
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/books/pomodoro"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>
<script>
    // Header category mega menu
    $(".menu-dots-img-wrp").click(function(){
        $(".wrp-new-posi-changes-whitelabel").toggleClass("menu-actives");
        $(".menu-overlay-big-menus").toggleClass("actv");
        $(".main-menu-wrp-whitelabel-big").toggleClass("menu-showing");
    });

    $(".menu-overlay-big-menus").click(function(){
        $(".wrp-new-posi-changes-whitelabel").removeClass("menu-actives");
        $(".menu-overlay-big-menus").removeClass("actv");
        $(".main-menu-wrp-whitelabel-big").removeClass("menu-showing");
    });
</script>
<script>
    <%if("true".equals(params.scarchCodeOpen)){%>
    signupWithFunction('openAccessCodePage','Sign up and use your scratch code.');
    <%}%>
    const theme_color = window.getComputedStyle(document.documentElement).getPropertyValue('--theme-color');
    let wileySite = false;
    <%if(session['wileySite'] == true){%>
    wileySite = true;
    <%}%>

    window.addEventListener('keydown', function(event) {
        if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
            event.preventDefault();
            if (event.stopImmediatePropagation) {
                event.stopImmediatePropagation();
            } else {
                event.stopPropagation();
            }
            return;
        }
    }, true);

    if($.browser.platform == "win") {
        $("html").addClass("windows");
    }

    if($.browser.name == "chrome") {
        $("html").addClass("chrome");
    } else if($.browser.name == "mozilla") {
        $("html").addClass("mozilla");
    } else if($.browser.name == "safari") {
        $("html").addClass("safari");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
                $('.btn-primary-modifier').css('background-color',theme_color);
                $('.btn-success-modifier').css('background-color','#27AE60 !important');
                $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
                $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
                $('.btn-warning-modifier').css('background-color','#FFD602 !important');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    } else {
        $("html").addClass("others");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    }


    var showContactus = "${session['enableContactus']}";
    var customMenuFooterHTML = '';
    var menuFooterLinks = document.querySelector('#customPageFooterLink');
    if (customMenus.length>0){
        customMenus.map(item=>{
            if (item.showInFooter=='true' || item.showInFooter==true){
                if (document.getElementById('customMenuWrap')!=null){
                    document.getElementById('customMenuWrap').classList.remove('d-none')
                    customMenuFooterHTML += "<li><a href='/${session['siteName']}/page/"+item.link+"?pageId="+item.id+"' target='_blank'>"+item.name+"</a></li>";
                }
            }
        })
    }

    menuFooterLinks ? menuFooterLinks.innerHTML += customMenuFooterHTML :""

</script>



<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>
<%if("ibookso".equals(session["siteName"])){%>
<g:render template="/printbooks/printSearch"></g:render>
<%}%>
