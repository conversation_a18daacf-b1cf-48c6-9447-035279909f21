<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required Meta Tags -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no">
    <meta name="theme-color" content="#2EBAC6" />
    <%if("75".equals(""+session["siteId"])){%>
    <meta name="google-site-verification" content="_Gvk_MwbsunTGNeOwPXLPAdJNHMLR3_iqZ0llu0sAcM"/>
    <%}%>
    <%if(session['wileySite'] ){%>
    <title>Computing | Artificial Intelligence | Data Science | Python | Job Interviews | Wiley </title>
    <%}else {
       String displayTitle = "Buy AI-Powered Books & eBooks Online | Mock Tests for 2026 Exams | GPTSir"
         if(title!=null) displayTitle = title
        else if(session["siteTitle"]!=null) displayTitle = session["siteTitle"]

    %>
    <title><%= displayTitle%></title>
    <%}%>
    <%
        String displayDesc = "Shop AI-powered textbooks, eBooks & mock tests at GPTSir. Smart learning tools, NCERT solutions, competitive exam prep, and fast delivery across India."
        if(seoDesc!=null) displayDesc = seoDesc
        else if(session["siteDescription"]!=null) displayDesc = session["siteDescription"]
        String metaKeywords ="buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book."
        if(keywords!=null) metaKeywords = keywords
        if(session["keywords"]!=null) metaKeywords=session["keywords"]

        String displayOGTitle = "All-India Online Test Series and Best books for CBSE | ICSE | CUET | NEET | JEE | Olympiads | Competitive Exams"
        if(title!=null) displayOGTitle = title
        else if(session["siteTitle"]!=null) displayOGTitle = session["siteTitle"]
    %>
    <meta name="description" content="${displayDesc}">

    <%
        String serverURL = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
    %>
    <% if("55".equals(""+session['siteId'])){ %>
    <meta content="${displayOGTitle}" property="og:title">
    <meta content="${displayDesc}" property="og:description">
    <meta content="${serverURL}/assets/privatelabels/educart-og-img.png" id="ogimage" property="og:image">
    <% }%>


    <% if("88".equals(""+session['siteId'])){ %>
        <g:if test="${request.forwardURI == '/aistore'}">
            <meta property="og:title" content="Buy AI Textbooks & eBooks Online | GPTSir Store">
            <meta property="og:description" content="AI-powered learning books, NCERT solutions, and exam preparation materials. Shop now on GPTSir.">
            <meta property="og:url" content="https://www.gptsir.ai/aistore">
            <meta property="og:image" content="${assetPath(src: 'gptsirlandingpage/gptsirai-logo.png', absolute: true)}" />
            <meta name="twitter:card" content="summary_large_image">
            <link rel="canonical" href="https://www.gptsir.ai/aistore" />
            <%@ page import="groovy.json.JsonOutput" %>
            <script type="application/ld+json">
                <%
                        def orgSchema = [
                                "@context": "https://schema.org",
                                "@type"   : "Organization",
                                "name"    : "GPTSir",
                                "url"     : "https://www.gptsir.ai",
                                "logo"    : assetPath(src: 'gptsirlandingpage/gptsirai-logo.png', absolute: true),
                                "sameAs"  : [
                                        "https://www.facebook.com/profile.php?id=61561621995625",
                                        "https://www.instagram.com/gptsir_/",
                                        "https://www.youtube.com/channel/UC-87LRCX4mo9jmPCqG3S9ZQ"
                                ]
                        ]
                        out << JsonOutput.toJson(orgSchema)
                %>
                <%= (orgSchema as grails.converters.JSON).toString() %>
            </script>
        </g:if>
    <%}%>

    <meta name="keywords" content="${metaKeywords}" />
    <meta name="generator" content="${session['siteName']} Books" />
    <meta name="author" content="webmaster - ${session['siteName']} Books and Learning Pvt Ltd">
    <meta name="subject" content="Book Store Online : Buy Books Online from ${session['siteName']} Books Store">
    <meta name="keyphrase" content="Medical Exam Books, Board exams books, CBSE Exam Books, UGC Net, Air Force Books, state exam books, Govt Exam Books, NDA Exam Books, Bank Po Books, Entrance Exam Books, Engineering Books, Exam Books, General Books, General English Books, General Knowledge Books, NDA & CDS Books, SBI exam books, competition books in Hindi, ssc competition books, civil service books, banking Exams books, Gate, Teacher exam Books, buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book.">
    <meta name="abstract" content="Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam.">

    <!-- Favicon -->
    <link rel="shortcut icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon"/>
    <link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
    <link rel="android-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}"/>
    <link rel="windows-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" />

    <!-- Google Fonts & Icons -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>
    <!-- Default CSS -->
    <asset:stylesheet href="whitelabel/style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="/assets/katex.min.css"/>
    <asset:stylesheet href="wonderslate/material.css" async="true"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
    <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
    <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />

    <!-- Override & Template CSS -->
    <asset:stylesheet href="whitelabel/override_style.css" async="true"/>
    <%if("true".equals(commonTemplate)){%>
    <asset:stylesheet href="whitelabel/siteTemplate.css" async="true"/>
    <% }%>
    <%if(session["customLandingPage"]!=null){%>
    <asset:stylesheet href="wonderslate/wsTemplate.css" async="true" media="all"/>
    <% }%>

    <%if(session['wileySite'] ==true){%>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost&display=swap" rel="stylesheet">
    <%}%>
    <!-- Default JS -->
    <asset:javascript src="jquery-1.11.2.min.js"/>
    <script src="/assets/katex.min.js"></script>
    <script src="/assets/auto-render.min.js"></script>

    <!-- Dynamic CSS Variables -->
    <style>
    :root {
        --theme-color: ${session["themeColor"]};
    }
    <%= session["customStyles"]?.replaceAll("&#64;", "@") %>
    </style>

    <%if(session['wileySite'] ==true){%>
    <style>
    html, body, h1, h2, h3, h4, h5, h6, p, li, a,
    input, textarea, select, label, span, small,
    button, th, td, dl, dt, dd, address{
        font-family: 'Jost', sans-serif !important;
    }
    .page-main-wrapper{
        min-height: 700px !important;
    }
    ul.link-ul-footer li a{
        font-size: 1rem !important;
        font-weight: 600 !important;
    }
    .copy-right-text-footer span{
        color: #212121 !important;
    }
    </style>
    <%}%>


    <%if("mobile".equals(params.mode)){%>
        <style>
        header,footer, .headerCategoriesMenu{
            display: none !important;
        }
        </style>
    <%}%>

    <% if("70".equals(""+session['siteId'])){ %>
    <!-- Facebook Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window,document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '2043910402638708');
        fbq('track', 'PageView');
    </script>
    <noscript>
        <img height="1" width="1" src="https://www.facebook.com/tr?id=2043910402638708&ev=PageView&noscript=1"/>
    </noscript>
    <!-- End Facebook Pixel Code -->
    <%}%>
    <% if("55".equals(""+session['siteId'])){ %>
    <!-- Meta Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '347355736654825');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=347355736654825&ev=PageView&noscript=1"/></noscript>
    <!-- End Meta Pixel Code -->
    <%}%>
    <% if("71".equals(""+session['siteId'])){ %>
    <!-- Meta Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '1127059251949505');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1127059251949505&ev=PageView&noscript=1"/></noscript>
    <!-- End Meta Pixel Code -->
    <%}%>
    <script async src="https://www.googletagmanager.com/gtag/js?id=${session["googleAnalyticsId"]}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        <%if(session["googleAnalyticsId"]!=null&&!"null".equals(session["googleAnalyticsId"])&&!"".equals(session["googleAnalyticsId"])){%>
        gtag('config', '${session["googleAnalyticsId"]}');
        <%}%>
        <%if(session['wileySite'] ==true){%>
        gtag('config', 'G-MR0QYLSDN5');
        <%}%>
    </script>
</head>
<%if("ibookso".equals(session["siteName"])){%>
<style>
.whitelabel-site{
    background-color: #F4F5FA !important;
}
</style>
<%}%>
<body class="whitelabel-site ws-whitelabel">

<sec:ifNotLoggedIn>
    <%
        boolean otpLogin=false;
        if ("true".equals(session['enableOTPLogin'])&&request.getServerName().indexOf("publish.")==-1){
            otpLogin=true
        }
    %>
    <%if(params.signUpPage !='true' && !otpLogin){%>
    <g:render template="/books/signIn"></g:render>
    <%}else if(otpLogin){%>
    <g:render template="/privatelabel/otpOnlyLogin"></g:render>
    <%}%>
</sec:ifNotLoggedIn>

<%if(params.tokenId==null&&session["appType"]==null){%>
<header>
    <div id="wrapper" class="wrp-cart">
        <div class="menu-main-min-height">
            <div class="main-menu-wrp" id="nav">
                <div class="container-fluid">
                    <div class="row justify-content-between">

                        <div class="pl-3 posi-static-respons">
                            <div class="user-menu-wrp">
                                <div class="wrp-new-posi-changes-whitelabel">
                                    <a href="javascript:void(0)" class="menu-dots-img-wrp">
                                        <img src="${assetPath(src: 'whitelabel/menu-dots.svg')}" class="menu-dots-img">
                                        <img src="${assetPath(src: 'whitelabel/menu-close-menus.png')}" class="menu-close-btn-menub" />
                                    </a>
                                    <div class="main-menu-wrp-whitelabel-big">
                                        <div class="container-fluid">
                                            <div class="row">
                                                <div class="col-12 col-md-3 col-xl-2 background-white-main">
                                                    <div class="manage-logo-big-menu-whitelabel">
                                                        <img src="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["logo"]}" alt="${session["siteName"]} Logo">
                                                    </div>
                                                    <div class="main-side-bar-side-menu-big-wrp-wala-whitelabel">
                                                        <ul class="this-is-side-wrp-ul-big-menu-whitelabel">
                                                            <li class="active-menuss"><a href="javascript:void(0)">Books</a></li>
                                                        </ul>
                                                        <ul class="this-is-side-wrp-ul-big-menu-whitelabel">
                                                            <sec:ifNotLoggedIn>
                                                                <li><a href="javascript:loginOpen()">My Account</a></li>
                                                                <%if("true".equals(session["showAccessCode"])){%>
                                                                <li><a href="javascript:signupWithFunction('openScratchCodePage','Sign up and use your scratch code.')">Scratch Code</a></li>
                                                                <%}%>
                                                                <%if("true".equals(""+session['showCurrentAffairs'])){%>
                                                                <li>
                                                                    <a href="/current-affairs">Current Affairs</a>
                                                                </li>
                                                                <%}%>
                                                            </sec:ifNotLoggedIn>
                                                            <%if("71".equals(""+session['siteId'])){%>
                                                                <li><a href="/liveMockTests">Live Mock Tests</a></li>
                                                            <%}%>
                                                            <sec:ifLoggedIn>
                                                                <li><a href="/creation/userProfile">My Account</a></li>
                                                                <%if("true".equals(session["showAccessCode"])){%>
                                                                <li><a href="/wsLibrary/accessCode">Scratch Code</a></li>
                                                                <%}%>
                                                                <%if("true".equals(""+session['showCurrentAffairs'])){%>
                                                                <li>
                                                                    <a href="/current-affairs">Current Affairs</a>
                                                                </li>
                                                                <%}%>
                                                                <%if("true".equals(""+session['showMockTests'])){%>
                                                                <li>
                                                                    <a href="/online-tests">Daily Tests</a>
                                                                </li>
                                                                <%}%>
                                                                <%if(!"Yes".equals(""+session["disableStore"])){%>
                                                                <li><a href="/usermanagement/orders">My Orders</a></li>
                                                                <%}%>
                                                                <%if("true".equals(""+session["showAnalytics"])){%>
                                                                <li class="nav-item">
                                                                    <a href="/aireport/dashboard" class="nav-link">Analytics</a>
                                                                </li>
                                                                <%}%>
                                                                <li><a href="javascript:logout()">Logout</a></li>
                                                            </sec:ifLoggedIn>

                                                        </ul>

                                                    </div>
                                                </div>
                                                <div class="col-md-9 col-xl-10 whitelabel-big-menu-side-wrp">
                                                    <div class="book-wrapper-sec-part-main-menu-big">
                                                        <div class="row" id="topMenuItems">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-overlay-big-menus"></div>
                            </div>
                            <div class="logo-white-small-wrp">
                                <a href="/sp/${session['siteName']}" class="white-logo-anchor-white">
                                    <img src="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["logoIcon"]}" alt="${session['siteName']} Logo"/>
                                </a>
                            </div>
                            <div class="overlay-menu-close"></div>
                        </div>
                        <%if("ibookso".equals(session["siteName"])){%>
                        <div class="d-flex justify-content-start global-search pr-0 pr-md-3 col">
                            <form class="form-inline rounded rounded-modifier col-12 col-lg-10 col-xl-8 pl-0 px-md-3 mb-0">
                                <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-print-book-store-navbar" autocomplete="off" placeholder="Product search">
                                <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="submitPrintSearch('search-print-book-store-navbar');" id="print-search-btn-header"><i class="material-icons">search</i></button>
                            </form>
                        </div>
                        <%}else{%>

                        <%}%>
                        <div class="searchFormWrapper d-none">
                            <form class="form-inline rounded rounded-modifier col-12 p-0 searchForm">
                                <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                                <i class="material-icons-round" onclick="showSearchForm()">close</i>
                            </form>
                        </div>
                        <div class="pr-md-3">
                            <div class="menu-wrp-all-users-com">
                                <ul class="menuLinks headerMenuLinks">
                                    <%if(!"ibookso".equals(session["siteName"])){%>
                                    <%String storeTxt = session['wileySite'] ? 'Catalog' : 'Store' %>
                                    <%if(!"Yes".equals(""+session["disableStore"])){%>
                                    <li class="link-menu-li navbar_cart"><a href="javascript:showSearchForm();" class="d-block"><i class="material-icons-round search-open-icon">search</i><i class="material-icons-round search-close-icon d-none">close</i></a></li>

                                    <li class="link-menu-li d-none d-md-block"><a href="/sp/${session['siteName']}/store" class="menu-link-anchor"><span>${storeTxt}</span></a></li>

                                    <li class="link-menu-li navbar_cart"><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon"><img src="${assetPath(src: 'whitelabel/shopping-cart.png')}" alt="" title="" /><span class="hide_mobile">Cart</span> <span class="cart_count" id="navbarCartCount">0</span></a></li>
                                    <%}}%>
                                    <%if("true".equals(""+session['showCurrentAffairs'])){%>
                                    <li class="header-menu-item d-none d-md-block">
                                        <a href="/current-affairs" class="header-menu-item-link">Current Affairs</a>
                                    </li>
                                    <%}%>
                                    <%if("71".equals(""+session['siteId'])){%>
                                        <li class="header-menu-item d-none d-md-block"><a href="/liveMockTests">Live Mock Tests</a></li>
                                    <%}%>
                                    <sec:ifNotLoggedIn>
                                        <% if(showLibrary){%>
                                        <%if("true".equals(""+session["digitalLibraryLandingPage"])){%>
                                        <li class="none-add-class-responsive d-none d-md-block"><a   href="/digitalLibrary"><span class="hide_mobile">Digital Library</span></a></li>
                                        <%}else{%>
                                            <% if("71".equals(""+session['siteId'])){ %>
                                                <li class="none-add-class-responsive d-none d-md-block"><a   href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'whitelabel/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Tests</span></a></li>
                                            <%}else {%>
                                                <li class="none-add-class-responsive d-none d-md-block"><a   href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'whitelabel/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Books</span></a></li>
                                            <%}%>
                                        <%}%>
                                        <%}%>
                                        <%if("true".equals(session["showAccessCode"])){%>
                                        <li class="header-menu-item d-none d-md-block"><a href="javascript:signupWithFunction('openScratchCodePage','Sign up and use your scratch code.')">Scratch Code</a></li>
                                        <%}%>
                                        <li class="none-add-class-responsive"><a href="javascript:loginOpen()"><img src="${assetPath(src: 'whitelabel/user-login.png')}" alt="" title="" /><span class="hide_mobile">Login</span></a></li>
                                    </sec:ifNotLoggedIn>
                                    <sec:ifLoggedIn>
                                        <%if("true".equals(""+session["digitalLibraryLandingPage"])){%>
                                        <li class="none-add-class-responsive d-none d-md-block"><a   href="/digitalLibrary"><span class="hide_mobile">Digital Library</span></a></li>
                                        <%}else{%>
                                            <% if("71".equals(""+session['siteId'])){ %>
                                                <li class="none-add-class-responsive d-none d-md-block"><a   href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'whitelabel/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Tests</span></a></li>
                                            <%} else {%>
                                                <li class="none-add-class-responsive d-none d-md-block"><a   href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'whitelabel/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Books</span></a></li>
                                            <%}%>
                                        <%}%>
                                        <%if(!"Yes".equals(""+session["disableStore"])){%>
                                        <li class="none-add-class-responsive d-none d-md-block"><a   href="/usermanagement/orders"><span class="hide_mobile">My Orders</span></a></li>
                                        <%}%>
                                        <%if("true".equals(session["showAccessCode"])){%>
                                        <li><a href="/wsLibrary/accessCode">Scratch Code</a></li>
                                        <%}%>
                                    </sec:ifLoggedIn>
                                    <%if("85".equals(""+session["siteId"])){%>
                                    <sec:ifLoggedIn>
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/progress/progressReport" class="header-menu-item-link">Progress Tracker</a>
                                        </li>
                                    </sec:ifLoggedIn>
                                    <%}%>
                                    <%if(session["userdetails"]!=null&&session["userdetails"].affliationCd!=null){%>
                                    <li class="nav-item">
                                        <a href="/partner" class="nav-link">Affiliation</a>
                                    </li>
                                    <%}%>
                                    <%if("true".equals(""+session["enableTest"])){%>
                                    <li class="nav-item">
                                        <a href="/test-generator" class="nav-link">Online Test</a>
                                    </li>
                                    <%}%>
                                    <%if("true".equals(""+session["enableQuestionPaper"])){%>
                                    <li class="nav-item">
                                        <a href="/questionPaper/listQuestionPapers" class="nav-link">Question Papers</a>
                                    </li>
                                    <%}%>
                                    <%if("true".equals(""+session["showAnalytics"])){%>
                                    <li class="nav-item">
                                        <a href="/aireport/dashboard" class="nav-link">Analytics</a>
                                    </li>
                                    <%}%>

                                    <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR,ROLE_WS_CONTENT_ADMIN,ROLE_FINANCE,ROLE_AFFILIATION_SALES,ROLE_INSTITUTE_ADMIN,
                                    ROLE_INSTITUTE_REPORT_MANAGER,ROLE_LIBRARY_USER_UPLOADER,ROLE_INFORMATION_ADMIN,ROLE_CUSTOMER_SUPPORT,ROLE_WS_SALES_TEAM,ROLE_WS_GROUP_ADMIN,ROLE_EXTERNAL_SALES_VIEWER,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/privatelabel/admin" class="header-menu-item-link">Admin</a>
                                        </li>
                                    </sec:ifAnyGranted>
                                    <sec:ifAllGranted roles="ROLE_GPT_PAGE_MANAGER">
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/intelligence/admin?siteId=${session["siteId"]}&key=aKu89JSb3&publisherId=${session["userdetails"].publisherId}" class="header-menu-item-link">GPT Books</a>
                                        </li>
                                    </sec:ifAllGranted>
                                    <sec:ifAllGranted roles="ROLE_INSTITUTE_MANAGER">
                                        <%if(session["instituteManagerInstituteId"]!=null){%>
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/instManager/adminDashboard?instituteId=${session["instituteManagerInstituteId"]}" class="header-menu-item-link">Manage Institute</a>
                                        </li>
                                        <%}%>
                                    </sec:ifAllGranted>
                                    <sec:ifAllGranted roles="ROLE_IBOOKGPT_SITE_ADMIN">
                                       <li class="header-menu-item d-none d-md-block">
                                            <a href="/instManager/listInstitutes" class="header-menu-item-link">Manage Institutes</a>
                                        </li>
                                    </sec:ifAllGranted>
                                    <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/wsshop/orderManagement" class="header-menu-item-link">Order Management</a>
                                        </li>

                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/excel/addBulkUsersAndBooks" class="header-menu-item-link">User Upload</a>
                                        </li>
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/reports/getBulkUsersAddedReportInput" class="header-menu-item-link">User Upload Report</a>
                                        </li>

                                    </sec:ifAllGranted>
                                    <sec:ifAnyGranted roles="ROLE_CLIENT_ORDER_MANAGER, ROLE_CUSTOMER_SUPPORT">
                                        <li class="header-menu-item d-none d-md-block">
                                            <a href="/log/deleteUserBooks" class="header-menu-item-link">User Books</a>
                                        </li>
                                    </sec:ifAnyGranted>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="headerCategoriesMenu">
                    <div class="header__categories">
                        <ul class="header__categories-list" id="catList"></ul>
                    </div>
                    <div class="header__submenus" id="headerCategorySubMenus">
                        <p class="text-center subMenuTitleText"><strong id="subMenuTitle"></strong></p>
                        <div class="submenuLists">
                            <ul class="syllabusList"></ul>
                            <div class="listScrollArrow">
                                <div class="listScrollArrowBall"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-backdrop fade show headerBackdrop d-none" id="categoryMenuBackdrop"></div>
            </div>
        </div>
    </div>



    <%if(session['wileySite'] ==true){%>

    <!-- Meta Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '292484073463856');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
                   src=https://www.facebook.com/tr?id=292484073463856&ev=PageView&noscript=1
    /></noscript>
    <!-- End Meta Pixel Code → -->


    <%}%>
</header>
<%}%>
<script>
    var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var customMenus = JSON.parse("${session["customPageMenus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var gradesTags;
    <%if("55".equals(""+session["siteId"])){%>
    gradesTags = JSON.parse("${session["activeGrades_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    <%}%>
    var customMenuLinksHTML = '';
    var menuLinks = document.querySelector('.menuLinks');
    customMenus.map(item=>{
        if (item.showInHeader=='true' || item.showInHeader==true){
            customMenuLinksHTML += "<li class='header-menu-item d-none d-md-block'>"+
                "<a href='/${session['siteName']}/page/"+item.link+"?pageId="+item.id+"'>"+item.name+"</a>"+
                "</li>";
        }
    })
    menuLinks.innerHTML += customMenuLinksHTML
    function logout(){
        window.location.href = '/logoff';
    }

    function showSearchForm(){
        $("body").toggleClass('showing-search-form');
        $(".search-open-icon,.search-close-icon,header .navbar-search,.searchFormWrapper").toggleClass("d-none");
        $(".headerMenuLinks,.posi-static-respons").toggleClass("d-none");
        $(".searchFormWrapper").toggleClass("d-flex");
        if($("body").hasClass("showing-search-form")) {
            $("#search-book-header").focus();
        }
    }

    function updateHeaderCategories(){
        let catItems="";
        var catList = levelTags;
        <%if("55".equals(""+session["siteId"])){%>
            catList = syllabusTags;
        <%}%>

        const catListElement = document.getElementById('catList');

        catList.forEach((cat,index)=>{
            var catName = cat.level.replaceAll(' ','-');
            <%if("55".equals(""+session["siteId"])){%>
              catName = cat.syllabus.replaceAll(' ','-');
            catItems += "<li class='header__categories-list__item'>" +
                "<a href='/sp/${session['siteName']}/store?syllabus="+catName+"' class='headerLevel' target='_blank'>"+cat.syllabus+"</a>"+
                "</li>";
            <%}else{%>
            catItems += "<li class='header__categories-list__item'>" +
                "<a href='/sp/${session['siteName']}/store?level="+catName+"' class='headerLevel' target='_blank'>"+cat.level+"</a>"+
                "</li>";
            <%}%>

        });
        catListElement.innerHTML = catItems;
        const hoverElement = document.querySelectorAll('.headerLevel');
        const showDiv = document.getElementById('headerCategorySubMenus');
        const handleMouseLeave = () => {
            addRemoveBackDrop('hide',showDiv);
        };
        hoverElement.forEach(elem=>{
            elem.addEventListener('mouseover', () => {
                updateSubMenus(elem.textContent)
                addRemoveBackDrop('show',showDiv);
            });
            showDiv.addEventListener('mouseout', handleMouseLeave);
            showDiv.addEventListener('mouseover', ()=>{
                addRemoveBackDrop('show',showDiv);
            });
            document.querySelector('#nav .container-fluid').addEventListener('mouseover',handleMouseLeave);
            document.addEventListener('click',handleMouseLeave);
        })
    }
    function updateSubMenus(hoveredText){
        let syllabusListHTML = "";
        const syllabusListDiv =  document.querySelector('.syllabusList');
        const headerCategorySubMenus = document.getElementById('headerCategorySubMenus');
        document.getElementById('subMenuTitle').innerHTML = hoveredText;
        const listScrollArrow = document.querySelector('.listScrollArrow');
        let syllabusCount = 0;
        <%if("55".equals(""+session["siteId"])){%>
        var gradeText
        gradesTags.forEach(grade=>{
            if (grade.syllabus === hoveredText){
                if(grade.level=="School") gradeText="Class "+grade.grade
                else gradeText = grade.grade
                let gradeLink = grade.syllabus.replaceAll(" ",'-');
                gradeLink += "&grade="+ grade.grade.replaceAll(" ",'-');
                syllabusListHTML += "<li><a href='/sp/${session['siteName']}/store?syllabus="+gradeLink+"' target='_blank'>"+gradeText+"</a></li>";
                syllabusCount++;
            }
        })
        <%}else{%>
        syllabusTags.forEach(syllabus=>{
            if (syllabus.level === hoveredText){
                let syllabusLink = syllabus.level.replaceAll(" ",'-');
                syllabusLink += "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-');
                syllabusListHTML += "<li><a href='/sp/${session['siteName']}/store?level="+syllabusLink+"' target='_blank'>"+syllabus.syllabus+"</a></li>";
                syllabusCount++;
            }
        })
        <%}%>
        syllabusListDiv.innerHTML = syllabusListHTML;
        setTimeout(()=>{
            if (headerCategorySubMenus.offsetHeight >= 500 && syllabusCount >= 30){
                listScrollArrow.style.display = 'flex';
            }else{
                listScrollArrow.style.display = 'none';
            }
        })
    }
    function addRemoveBackDrop(action,showDiv){
        const categoryMenuBackdrop = document.getElementById('categoryMenuBackdrop');
        const headerElement = document.querySelector('#nav .container-fluid');
        const headerCategoriesMenu = document.querySelector('.headerCategoriesMenu');
        if(action=='show'){
            showDiv.style.display = 'block';
            showDiv.style.opacity = '1';
            categoryMenuBackdrop.classList.remove('d-none');
            categoryMenuBackdrop.style.top = headerElement.offsetHeight + headerCategoriesMenu.offsetHeight +10+'px';
        }else if(action=='hide'){
            showDiv.style.display = 'none';
            showDiv.style.opacity = '0';
            categoryMenuBackdrop.classList.add('d-none');
        }
    }
    if (levelTags.length>0){
        updateHeaderCategories();
    }else {
        document.querySelector('.headerCategoriesMenu').classList.add('d-none');
    }

    if ($(window).width() < 767){
        let accordionHTMl = "";
        let accID = "";
        accordionHTMl +="<div class='accordion' id='accordion'>";
        levelTags.forEach((cat,index)=>{
            accID = index;
            accordionHTMl += "<div class='card mb-3'>" +
                "<div class='card-header p-2' id='heading-"+index+"'>";
            if(index==0){
                accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
            }else{
                accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0 collapsed' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
            }
            accordionHTMl +="<a href='/sp/${session['siteName']}store?level="+cat.level.replaceAll(" ",'-')+"' target='_blank'><p>"+cat.level+"</p></a>"+
                "</button>" +
                "</div>";
            if (index==0){
                accordionHTMl +="<div id='collapse-"+accID+"' class='collapse show' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
            }else{
                accordionHTMl +="<div id='collapse-"+accID+"' class='collapse' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
            }
            accordionHTMl +="<div class='card-body'>";
            syllabusTags.forEach((syllabus,index)=>{
                if (cat.level === syllabus.level){
                    accordionHTMl +="<a href='/sp/${session['siteName']}/store?level="+cat.level+"&"+ "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-')+"'  target='_blank'><p>"+syllabus.syllabus+"</p></a>";
                }
            })
            accordionHTMl +="</div>"+
                "</div>"+
                "</div>";
        })
        accordionHTMl+="</div>";
        document.querySelector('.whitelabel-big-menu-side-wrp').innerHTML = accordionHTMl;
    }
    function openScratchCodePage(){
        window.location.href ="/wsLibrary/accessCode";
    }
</script>
